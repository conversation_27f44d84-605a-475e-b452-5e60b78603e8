spring.application.name=openai

# Server configuration
server.port=8080

# Logging pattern
logging.pattern.console=%green(%d{HH:mm:ss.SSS}) %blue(%-5level) %red([%thread]) %yellow(%logger{15}) - %msg%n

# OpenAI Configuration
spring.ai.openai.api-key=${OPENAI_API_KEY}

# Optional: Set the OpenAI model (default is gpt-3.5-turbo)
spring.ai.openai.chat.model=gpt-3.5-turbo

# Optional: Set the base URL (default is https://api.openai.com)
# spring.ai.openai.base-url=https://api.openai.com
