spring.application.name=openai

# Server configuration
server.port=8080

# OpenAI Configuration
# Replace 'your-openai-api-key-here' with your actual OpenAI API key
spring.ai.openai.api-key=your-openai-api-key-here

# Optional: Set the OpenAI model (default is gpt-3.5-turbo)
spring.ai.openai.chat.model=gpt-3.5-turbo

# Optional: Set the base URL (default is https://api.openai.com)
# spring.ai.openai.base-url=https://api.openai.com
